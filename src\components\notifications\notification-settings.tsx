"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Loader2, Save, RotateCcw } from "lucide-react";
import { useUpdateNotificationSettings } from "@/hooks/notifications/use-notifications";

interface NotificationSettingsProps {
  settings: INotificationSettings;
  isLoading?: boolean;
}

// Notification preferences configuration
const NOTIFICATION_PREFERENCES = [
  {
    key: "agent_assigned" as keyof INotificationSettings,
    label: "Agent Assigned",
    description: "Get notified when an agent is assigned to your application",
    type: "boolean" as const,
  },
  {
    key: "case_status_update" as keyof INotificationSettings,
    label: "Case Status Updates",
    description: "Receive updates when your application status changes",
    type: "boolean" as const,
  },
  {
    key: "agent_query" as keyof INotificationSettings,
    label: "Agent Queries",
    description:
      "Get notified when agents have questions about your application",
    type: "boolean" as const,
  },
  {
    key: "document_rejection" as keyof INotificationSettings,
    label: "Document Rejection",
    description: "Receive alerts when submitted documents are rejected",
    type: "boolean" as const,
  },
  {
    key: "missing_document_reminder_days" as keyof INotificationSettings,
    label: "Missing Document Reminders",
    description: "Number of days after sending reminders for missing documents",
    type: "number" as const,
    min: 1,
    max: 365,
  },

  {
    key: "final_decision_issued" as keyof INotificationSettings,
    label: "Final Decision",
    description:
      "Get notified when final decisions are issued for your applications",
    type: "boolean" as const,
  },
];

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  isLoading = false,
}) => {
  const [localSettings, setLocalSettings] =
    useState<INotificationSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const updateMutation = useUpdateNotificationSettings();

  // Update local settings when props change
  useEffect(() => {
    setLocalSettings(settings);
    setHasChanges(false);
    setValidationErrors({});
  }, [settings]);

  // Detect changes
  useEffect(() => {
    const changed = JSON.stringify(localSettings) !== JSON.stringify(settings);
    setHasChanges(changed);
  }, [localSettings, settings]);

  // Validation function for reminder days
  const validateReminderDays = useCallback((value: number): string | null => {
    if (value < 1) return "Must be at least 1 day";
    if (value > 365) return "Cannot exceed 365 days";
    if (!Number.isInteger(value)) return "Must be a whole number";
    return null;
  }, []);

  // Handle setting changes
  const handleSettingChange = useCallback(
    (key: keyof INotificationSettings, value: boolean | number) => {
      // Clear validation error for this field
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });

      // Validate reminder days if applicable
      if (
        key === "missing_document_reminder_days" &&
        typeof value === "number"
      ) {
        const error = validateReminderDays(value);
        if (error) {
          setValidationErrors((prev) => ({
            ...prev,
            [key]: error,
          }));
          return; // Don't update if validation fails
        }
      }

      setLocalSettings((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [validateReminderDays]
  );

  // Handle save
  const handleSave = useCallback(async () => {
    // Check for validation errors
    const hasErrors = Object.keys(validationErrors).length > 0;
    if (hasErrors) {
      return;
    }

    try {
      await updateMutation.mutateAsync(localSettings);
      setHasChanges(false);
    } catch (error) {
      console.error("Failed to update notification settings:", error);
    }
  }, [localSettings, validationErrors, updateMutation]);

  // Handle reset
  const handleReset = useCallback(() => {
    setLocalSettings(settings);
    setHasChanges(false);
    setValidationErrors({});
  }, [settings]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        {[1, 2, 3, 4, 5, 6, 7, 8].map((index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
                  <div className="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
                </div>
                <div className="h-6 w-11 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action buttons */}
      {hasChanges && (
        <div className="flex items-center gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex-1">
            <p className="text-sm font-medium text-blue-900">
              You have unsaved changes
            </p>
            <p className="text-xs text-blue-700">
              Save your changes or reset to discard them
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={updateMutation.isPending}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={
                updateMutation.isPending ||
                Object.keys(validationErrors).length > 0
              }
            >
              {updateMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-1" />
              )}
              Save Changes
            </Button>
          </div>
        </div>
      )}

      {/* Notification preferences */}
      <div className="grid gap-4">
        {NOTIFICATION_PREFERENCES.map((preference) => {
          const value = localSettings[preference.key];
          const hasError = validationErrors[preference.key];

          return (
            <Card
              key={preference.key}
              className={hasError ? "border-red-200" : ""}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-base">
                      {preference.label}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {preference.description}
                    </CardDescription>
                    {hasError && (
                      <p className="text-sm text-red-600 mt-1">{hasError}</p>
                    )}
                  </div>
                  <div className="ml-4">
                    {preference.type === "boolean" ? (
                      <Switch
                        checked={value as boolean}
                        onCheckedChange={(checked) =>
                          handleSettingChange(preference.key, checked)
                        }
                        disabled={updateMutation.isPending}
                      />
                    ) : (
                      <div className="w-24">
                        <Input
                          type="number"
                          value={value as number}
                          onChange={(e) =>
                            handleSettingChange(
                              preference.key,
                              parseInt(e.target.value) || 1
                            )
                          }
                          min={preference.min}
                          max={preference.max}
                          disabled={updateMutation.isPending}
                          className={hasError ? "border-red-300" : ""}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
