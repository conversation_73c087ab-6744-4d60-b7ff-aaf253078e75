import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * PUT /api/applications/[id]/assign
 *
 * Assigns an agent to an application.
 * This endpoint supports both agent assignment and unassignment.
 * Admin-only functionality with proper authentication and error handling.
 *
 * @param {NextRequest} request - The incoming request containing agentId
 * @param {{ params: { id: string } }} params - Route parameters containing the application ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { agentId } = body;

    // Validate agentId (can be empty string for unassignment)
    if (agentId === undefined || agentId === null) {
      return NextResponse.json(
        { success: false, message: "Agent ID is required" },
        { status: 400 }
      );
    }

    // Assign agent in backend
    const backendUrl = `${apiUrl}/applications/${id}/assign`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ agentId }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to assign agent",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Agent assignment error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
