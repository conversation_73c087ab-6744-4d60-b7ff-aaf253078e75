import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * DELETE /api/applications/[id]/agents/[agentId]
 *
 * Removes an agent from an application.
 * Admin-only functionality with proper authentication and error handling.
 * Implements role-based access control to ensure only admins can remove agents.
 *
 * @param {NextRequest} request - The incoming request
 * @param {object} params - Route parameters containing application ID and agent ID
 * @param {string} params.id - Application ID
 * @param {string} params.agentId - Agent ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string; agentId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin (role-based access control)
    // Get user profile to check role
    const profileResponse = await fetch(`${apiUrl}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!profileResponse.ok) {
      return NextResponse.json(
        { success: false, message: "Failed to verify user role" },
        { status: 403 }
      );
    }

    const profileData = await profileResponse.json();
    const userRole = profileData?.data?.tokenType;

    // Only allow admin users to remove agents
    if (userRole !== "admin") {
      return NextResponse.json(
        {
          success: false,
          message: "Access denied. Admin privileges required.",
        },
        { status: 403 }
      );
    }

    const { id, agentId } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    if (!agentId) {
      return NextResponse.json(
        { success: false, message: "Agent ID is required" },
        { status: 400 }
      );
    }

    // Remove agent from application in backend
    const backendUrl = `${apiUrl}/applications/${id}/agents/${agentId}`;

    const response = await fetch(backendUrl, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to remove agent",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Agent removal error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
